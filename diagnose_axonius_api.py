#!/usr/bin/env python3
"""
Axonius API Diagnostic Tool

This script helps diagnose API connectivity issues and discover
the correct endpoints for your Axonius instance.
"""

import requests
import json
import sys
import argparse
from urllib.parse import urljoin
import urllib3

# Disable SSL warnings for testing
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def test_endpoint(base_url, endpoint, headers, verify_ssl=True, timeout=10):
    """Test a specific endpoint and return response info."""
    try:
        url = urljoin(base_url, endpoint)
        response = requests.get(url, headers=headers, verify=verify_ssl, timeout=timeout)
        
        return {
            'endpoint': endpoint,
            'status_code': response.status_code,
            'success': response.status_code < 400,
            'content_type': response.headers.get('content-type', ''),
            'response_size': len(response.content),
            'error': None
        }
    except Exception as e:
        return {
            'endpoint': endpoint,
            'status_code': None,
            'success': False,
            'content_type': '',
            'response_size': 0,
            'error': str(e)
        }


def diagnose_axonius_api(base_url, api_key, api_secret, verify_ssl=True):
    """Diagnose Axonius API connectivity and discover endpoints."""
    
    print(f"Diagnosing Axonius API at: {base_url}")
    print("=" * 60)
    
    # Set up headers
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'api-key': api_key,
        'api-secret': api_secret
    }
    
    # Test basic connectivity
    print("\n1. Testing Basic Connectivity")
    print("-" * 30)
    
    try:
        response = requests.get(base_url, verify=verify_ssl, timeout=10)
        print(f"✓ Base URL accessible (Status: {response.status_code})")
    except Exception as e:
        print(f"✗ Base URL not accessible: {e}")
        return False
    
    # Test common API endpoints
    print("\n2. Testing Common API Endpoints")
    print("-" * 35)
    
    common_endpoints = [
        '/',
        '/api',
        '/api/v1',
        '/api/system',
        '/api/system/meta',
        '/api/system/status',
        '/api/v1/system',
        '/api/v1/system/meta',
        '/api/v1/system/status',
        '/api/devices',
        '/api/devices/count',
        '/api/v1/devices',
        '/api/v1/devices/count',
        '/api/assets',
        '/api/assets/devices',
        '/api/assets/devices/count',
        '/api/v1/assets',
        '/api/v1/assets/devices',
        '/api/v1/assets/devices/count'
    ]
    
    working_endpoints = []
    
    for endpoint in common_endpoints:
        result = test_endpoint(base_url, endpoint, headers, verify_ssl)
        
        status_icon = "✓" if result['success'] else "✗"
        status_code = result['status_code'] or 'ERR'
        
        print(f"{status_icon} {endpoint:<25} Status: {status_code:<3} Size: {result['response_size']:<6}")
        
        if result['success']:
            working_endpoints.append(endpoint)
        elif result['error']:
            print(f"    Error: {result['error']}")
    
    # Test authentication
    print("\n3. Testing Authentication")
    print("-" * 25)
    
    if working_endpoints:
        test_endpoint_for_auth = working_endpoints[0]
        result = test_endpoint(base_url, test_endpoint_for_auth, headers, verify_ssl)
        
        if result['status_code'] == 401:
            print("✗ Authentication failed - check API key and secret")
        elif result['status_code'] == 403:
            print("✗ Access forbidden - check permissions")
        elif result['success']:
            print("✓ Authentication appears to be working")
        else:
            print(f"? Authentication status unclear (Status: {result['status_code']})")
    else:
        print("? Cannot test authentication - no working endpoints found")
    
    # Try to get actual data from device endpoints
    print("\n4. Testing Device Data Endpoints")
    print("-" * 32)
    
    device_endpoints = [ep for ep in working_endpoints if 'device' in ep]
    
    if device_endpoints:
        for endpoint in device_endpoints:
            # Try to get a small sample of data
            test_params = {'page': 1, 'page_size': 1}
            try:
                url = urljoin(base_url, endpoint)
                response = requests.get(
                    url, 
                    headers=headers, 
                    params=test_params,
                    verify=verify_ssl, 
                    timeout=10
                )
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        entities = data.get('entities', data.get('assets', data.get('data', [])))
                        total = data.get('total', data.get('count', 'unknown'))
                        
                        print(f"✓ {endpoint} - Found {len(entities)} entities (total: {total})")
                        
                        # Show sample field names if available
                        if entities and isinstance(entities[0], dict):
                            sample_fields = list(entities[0].keys())[:5]
                            print(f"    Sample fields: {', '.join(sample_fields)}")
                        
                    except json.JSONDecodeError:
                        print(f"? {endpoint} - Response not JSON")
                else:
                    print(f"✗ {endpoint} - Status: {response.status_code}")
                    
            except Exception as e:
                print(f"✗ {endpoint} - Error: {e}")
    else:
        print("No device endpoints found to test")
    
    # Summary and recommendations
    print("\n5. Summary and Recommendations")
    print("-" * 30)
    
    if working_endpoints:
        print(f"✓ Found {len(working_endpoints)} working endpoints")
        print("\nWorking endpoints:")
        for endpoint in working_endpoints:
            print(f"  - {endpoint}")
        
        # Suggest best endpoints to use
        device_endpoints = [ep for ep in working_endpoints if 'device' in ep]
        if device_endpoints:
            print(f"\nRecommended device endpoint: {device_endpoints[0]}")
        
        system_endpoints = [ep for ep in working_endpoints if 'system' in ep or ep == '/api']
        if system_endpoints:
            print(f"Recommended system endpoint: {system_endpoints[0]}")
            
    else:
        print("✗ No working endpoints found")
        print("\nTroubleshooting suggestions:")
        print("1. Check if the base URL is correct")
        print("2. Verify API credentials")
        print("3. Check if SSL verification should be disabled")
        print("4. Confirm the Axonius instance is accessible")
    
    return len(working_endpoints) > 0


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Diagnose Axonius API connectivity')
    parser.add_argument('--url', required=True, help='Axonius base URL')
    parser.add_argument('--api-key', required=True, help='API key')
    parser.add_argument('--api-secret', required=True, help='API secret')
    parser.add_argument('--no-ssl-verify', action='store_true', help='Disable SSL verification')
    
    args = parser.parse_args()
    
    verify_ssl = not args.no_ssl_verify
    
    success = diagnose_axonius_api(
        base_url=args.url,
        api_key=args.api_key,
        api_secret=args.api_secret,
        verify_ssl=verify_ssl
    )
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
