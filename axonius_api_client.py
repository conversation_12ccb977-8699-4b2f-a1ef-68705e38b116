#!/usr/bin/env python3
"""
Axonius API Client with Paging Support

This module provides a comprehensive client for interacting with the Axonius API,
including support for pagination, authentication, and data parsing.
"""

import requests
import json
import time
import logging
import os
from typing import Dict, List, Optional, Generator, Any
from urllib.parse import urljoin
from dataclasses import dataclass
from datetime import datetime
import urllib3

# Disable SSL warnings if needed (for self-signed certificates)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class AxoniusConfig:
    """Configuration class for Axonius API connection."""
    base_url: str
    api_key: str
    api_secret: str
    verify_ssl: bool = True
    timeout: int = 30
    max_retries: int = 3
    retry_delay: int = 1


class AxoniusAPIError(Exception):
    """Custom exception for Axonius API errors."""
    pass


class AxoniusClient:
    """
    Axonius API Client with comprehensive paging and data retrieval support.
    """
    
    def __init__(self, config: AxoniusConfig):
        """
        Initialize the Axonius client.
        
        Args:
            config: AxoniusConfig object with connection parameters
        """
        self.config = config
        self.session = requests.Session()
        self.session.verify = config.verify_ssl
        
        # Set up authentication headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'api-key': config.api_key,
            'api-secret': config.api_secret
        })
        
        logger.info(f"Initialized Axonius client for {config.base_url}")
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make an HTTP request with retry logic.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            **kwargs: Additional arguments for requests
            
        Returns:
            requests.Response object
            
        Raises:
            AxoniusAPIError: If request fails after retries
        """
        url = urljoin(self.config.base_url, endpoint)
        
        for attempt in range(self.config.max_retries):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")
                
                response = self.session.request(
                    method=method,
                    url=url,
                    timeout=self.config.timeout,
                    **kwargs
                )
                
                # Check for HTTP errors
                if response.status_code == 401:
                    raise AxoniusAPIError("Authentication failed. Check API key and secret.")
                elif response.status_code == 403:
                    raise AxoniusAPIError("Access forbidden. Check permissions.")
                elif response.status_code == 429:
                    # Rate limiting - wait and retry
                    wait_time = self.config.retry_delay * (2 ** attempt)
                    logger.warning(f"Rate limited. Waiting {wait_time} seconds...")
                    time.sleep(wait_time)
                    continue
                elif response.status_code >= 400:
                    raise AxoniusAPIError(f"HTTP {response.status_code}: {response.text}")
                
                return response
                
            except requests.exceptions.RequestException as e:
                if attempt == self.config.max_retries - 1:
                    raise AxoniusAPIError(f"Request failed after {self.config.max_retries} attempts: {e}")
                
                wait_time = self.config.retry_delay * (2 ** attempt)
                logger.warning(f"Request failed (attempt {attempt + 1}): {e}. Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
        
        raise AxoniusAPIError("Maximum retries exceeded")
    
    def test_connection(self) -> bool:
        """
        Test the connection to Axonius API.

        Returns:
            bool: True if connection successful, False otherwise
        """
        # Try different common API endpoints for Axonius
        test_endpoints = [
            '/api/system/meta',
            '/api/v1/system/meta',
            '/api/system/status',
            '/api/v1/system/status',
            '/api/devices/count',
            '/api/v1/devices/count',
            '/api/assets/devices/count',
            '/api/v1/assets/devices/count'
        ]

        for endpoint in test_endpoints:
            try:
                logger.debug(f"Testing endpoint: {endpoint}")
                response = self._make_request('GET', endpoint)
                logger.info(f"Connection test successful using endpoint: {endpoint}")
                return True
            except AxoniusAPIError as e:
                if "404" in str(e):
                    logger.debug(f"Endpoint {endpoint} not found, trying next...")
                    continue
                else:
                    logger.error(f"Connection test failed with non-404 error: {e}")
                    return False
            except Exception as e:
                logger.debug(f"Endpoint {endpoint} failed: {e}")
                continue

        logger.error("Connection test failed: No valid API endpoints found")
        return False
    
    def get_devices_count(self, query: Optional[str] = None) -> int:
        """
        Get the total count of devices.

        Args:
            query: Optional query filter

        Returns:
            int: Total number of devices
        """
        # Try different endpoint patterns for device count
        count_endpoints = [
            '/api/devices/count',
            '/api/v1/devices/count',
            '/api/assets/devices/count',
            '/api/v1/assets/devices/count'
        ]

        params = {}
        if query:
            params['query'] = query

        for endpoint in count_endpoints:
            try:
                logger.debug(f"Trying count endpoint: {endpoint}")
                response = self._make_request('GET', endpoint, params=params)
                data = response.json()

                # Try different response formats
                # Handle JSON:API format used by Axonius
                if 'data' in data and isinstance(data['data'], dict) and 'attributes' in data['data']:
                    count = data['data']['attributes'].get('value', 0)
                else:
                    count = data.get('count', data.get('total', data.get('total_count', 0)))

                logger.debug(f"Got count {count} from endpoint {endpoint}")
                return count

            except AxoniusAPIError as e:
                if "404" in str(e):
                    logger.debug(f"Count endpoint {endpoint} not found, trying next...")
                    continue
                else:
                    raise e

        raise AxoniusAPIError("No valid device count endpoint found")
    
    def get_devices_paged(
        self,
        page_size: int = 100,
        query: Optional[str] = None,
        fields: Optional[List[str]] = None,
        sort_field: Optional[str] = None,
        sort_desc: bool = False,
        max_pages: Optional[int] = None
    ) -> Generator[Dict[str, Any], None, None]:
        """
        Get devices with pagination support.

        Args:
            page_size: Number of items per page (default: 100)
            query: Optional query filter
            fields: List of fields to retrieve
            sort_field: Field to sort by
            sort_desc: Sort in descending order
            max_pages: Maximum number of pages to retrieve (None for all)

        Yields:
            Dict containing page data with 'entities', 'page', 'total_pages', etc.
        """
        # Try different endpoint patterns and methods for devices
        # Based on exploration, Axonius uses GET with query parameters
        device_endpoints = [
            ('/api/devices', 'GET'),
            ('/api/v1/devices', 'GET'),
            ('/api/assets/devices', 'GET'),
            ('/api/v1/assets/devices', 'GET')
        ]

        working_endpoint = None
        working_method = None

        # Find a working endpoint and method
        for endpoint, method in device_endpoints:
            try:
                logger.debug(f"Testing devices endpoint: {method} {endpoint}")

                # Try GET with query parameters (based on exploration results)
                test_params = {
                    'page[size]': 1,  # Try JSON:API style pagination
                    'page[number]': 1
                }

                response = self._make_request('GET', endpoint, params=test_params)
                working_endpoint = endpoint
                working_method = method
                logger.debug(f"Found working devices endpoint: {method} {endpoint}")
                break

            except AxoniusAPIError as e:
                if "404" in str(e):
                    continue
                else:
                    # Try without pagination parameters
                    try:
                        response = self._make_request('GET', endpoint)
                        working_endpoint = endpoint
                        working_method = method
                        logger.debug(f"Found working devices endpoint: {method} {endpoint} (no pagination)")
                        break
                    except AxoniusAPIError:
                        logger.debug(f"Endpoint {method} {endpoint} failed with: {e}")
                        continue

        if not working_endpoint:
            raise AxoniusAPIError("No valid devices endpoint found")

        logger.info(f"Using endpoint: {working_method} {working_endpoint}")

        page = 1
        total_pages = None

        while True:
            logger.info(f"Fetching page {page} (page_size: {page_size})")

            try:
                # Use GET with query parameters (Axonius uses GET for device queries)
                params = {}

                # Try different pagination parameter formats
                if page > 1:  # Only add pagination if not first page
                    # Try JSON:API style first
                    params['page[number]'] = page
                    params['page[size]'] = page_size

                if query:
                    params['filter'] = query
                if fields:
                    params['fields'] = ','.join(fields)
                if sort_field:
                    sort_param = f"-{sort_field}" if sort_desc else sort_field
                    params['sort'] = sort_param

                response = self._make_request('GET', working_endpoint, params=params)
                data = response.json()

                # Handle JSON:API format - convert to our expected format
                if 'data' in data and isinstance(data['data'], list):
                    # Convert JSON:API format to our expected format
                    entities = []
                    for item in data['data']:
                        if 'attributes' in item:
                            entities.append(item['attributes'])
                        else:
                            entities.append(item)

                    # Create response in expected format
                    converted_data = {
                        'entities': entities,
                        'type': 'devices'
                    }

                    # Try to extract pagination info from meta
                    if 'meta' in data:
                        meta = data['meta']
                        if total_pages is None:
                            total_pages = meta.get('page_count', meta.get('total_pages', 1))

                    # If no pagination info found, estimate based on data
                    if total_pages is None:
                        if len(entities) < page_size:
                            total_pages = page  # This is likely the last page
                        else:
                            total_pages = page + 1  # Assume there's at least one more page

                    data = converted_data
                else:
                    # Handle other response formats
                    entities = data.get('entities', data.get('assets', data.get('data', [])))
                    if total_pages is None:
                        total_pages = data.get('total_pages', data.get('page_count', 1))

                logger.info(f"Total pages: {total_pages}")

                # Add pagination metadata to response
                data['current_page'] = page
                data['total_pages'] = total_pages
                data['page_size'] = page_size

                yield data

                # Check if we've reached the end or max pages
                entities = data.get('entities', [])
                if len(entities) == 0 or page >= total_pages or (max_pages and page >= max_pages):
                    break

                page += 1

            except Exception as e:
                logger.error(f"Error fetching page {page}: {e}")
                break
    
    def get_all_devices(
        self,
        query: Optional[str] = None,
        fields: Optional[List[str]] = None,
        page_size: int = 100,
        max_items: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Get all devices as a single list.
        
        Args:
            query: Optional query filter
            fields: List of fields to retrieve
            page_size: Number of items per page
            max_items: Maximum number of items to retrieve
            
        Returns:
            List of device dictionaries
        """
        all_devices = []
        items_collected = 0
        
        for page_data in self.get_devices_paged(
            page_size=page_size,
            query=query,
            fields=fields
        ):
            entities = page_data.get('entities', [])
            
            for entity in entities:
                if max_items and items_collected >= max_items:
                    return all_devices
                
                all_devices.append(entity)
                items_collected += 1
            
            logger.info(f"Collected {items_collected} devices so far...")
        
        logger.info(f"Total devices collected: {len(all_devices)}")
        return all_devices
    
    def save_devices_to_file(
        self,
        filename: str,
        query: Optional[str] = None,
        fields: Optional[List[str]] = None,
        page_size: int = 100,
        format_type: str = 'json'
    ) -> None:
        """
        Save devices data to a file with pagination.
        
        Args:
            filename: Output filename
            query: Optional query filter
            fields: List of fields to retrieve
            page_size: Number of items per page
            format_type: Output format ('json' or 'jsonl')
        """
        logger.info(f"Saving devices to {filename} (format: {format_type})")
        
        if format_type == 'json':
            # Save as single JSON file
            devices = self.get_all_devices(
                query=query,
                fields=fields,
                page_size=page_size
            )
            
            output_data = {
                'type': 'devices',
                'entities': devices,
                'total_count': len(devices),
                'timestamp': datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
                
        elif format_type == 'jsonl':
            # Save as JSON Lines format (one JSON object per line)
            with open(filename, 'w', encoding='utf-8') as f:
                for page_data in self.get_devices_paged(
                    page_size=page_size,
                    query=query,
                    fields=fields
                ):
                    for entity in page_data.get('entities', []):
                        f.write(json.dumps(entity, ensure_ascii=False) + '\n')
        
        logger.info(f"Data saved to {filename}")


def load_config_from_env() -> AxoniusConfig:
    """
    Load configuration from environment variables.
    
    Returns:
        AxoniusConfig object
        
    Raises:
        ValueError: If required environment variables are missing
    """
    required_vars = ['AXONIUS_URL', 'AXONIUS_API_KEY', 'AXONIUS_API_SECRET']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    return AxoniusConfig(
        base_url=os.getenv('AXONIUS_URL'),
        api_key=os.getenv('AXONIUS_API_KEY'),
        api_secret=os.getenv('AXONIUS_API_SECRET'),
        verify_ssl=os.getenv('AXONIUS_VERIFY_SSL', 'true').lower() == 'true',
        timeout=int(os.getenv('AXONIUS_TIMEOUT', '30')),
        max_retries=int(os.getenv('AXONIUS_MAX_RETRIES', '3')),
        retry_delay=int(os.getenv('AXONIUS_RETRY_DELAY', '1'))
    )


def load_config_from_file(config_file: str) -> AxoniusConfig:
    """
    Load configuration from a JSON file.
    
    Args:
        config_file: Path to configuration file
        
    Returns:
        AxoniusConfig object
    """
    with open(config_file, 'r') as f:
        config_data = json.load(f)
    
    return AxoniusConfig(**config_data)
