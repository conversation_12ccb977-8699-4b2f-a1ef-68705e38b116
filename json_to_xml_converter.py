#!/usr/bin/env python3
"""
JSON to XML Converter Script

This script converts the q4_3.json file to XML format.
It handles complex nested structures, arrays, and various data types.
"""

import json
import xml.etree.ElementTree as ET
from xml.dom import minidom
import argparse
import sys
from pathlib import Path


def sanitize_tag_name(name):
    """
    Sanitize tag names to be valid XML element names.
    Replace invalid characters with underscores.
    """
    # Replace dots and other invalid characters with underscores
    sanitized = name.replace('.', '_').replace('-', '_').replace(' ', '_')
    
    # Ensure the tag doesn't start with a number
    if sanitized and sanitized[0].isdigit():
        sanitized = 'item_' + sanitized
    
    # If empty or invalid, use a default name
    if not sanitized or not sanitized.replace('_', '').isalnum():
        sanitized = 'item'
    
    return sanitized


def convert_value_to_xml(parent, key, value):
    """
    Convert a JSON value to XML element and add it to the parent.
    Handles different data types: dict, list, str, int, float, bool, None
    """
    # Sanitize the key to be a valid XML tag name
    tag_name = sanitize_tag_name(str(key))
    
    if value is None:
        # Handle null values
        elem = ET.SubElement(parent, tag_name)
        elem.set('type', 'null')
        elem.text = ''
    elif isinstance(value, dict):
        # Handle dictionaries
        elem = ET.SubElement(parent, tag_name)
        elem.set('type', 'object')
        for sub_key, sub_value in value.items():
            convert_value_to_xml(elem, sub_key, sub_value)
    elif isinstance(value, list):
        # Handle arrays
        elem = ET.SubElement(parent, tag_name)
        elem.set('type', 'array')
        elem.set('length', str(len(value)))
        for i, item in enumerate(value):
            convert_value_to_xml(elem, f'item_{i}', item)
    elif isinstance(value, bool):
        # Handle booleans (must come before int check since bool is subclass of int)
        elem = ET.SubElement(parent, tag_name)
        elem.set('type', 'boolean')
        elem.text = str(value).lower()
    elif isinstance(value, int):
        # Handle integers
        elem = ET.SubElement(parent, tag_name)
        elem.set('type', 'integer')
        elem.text = str(value)
    elif isinstance(value, float):
        # Handle floats
        elem = ET.SubElement(parent, tag_name)
        elem.set('type', 'float')
        elem.text = str(value)
    else:
        # Handle strings and other types
        elem = ET.SubElement(parent, tag_name)
        elem.set('type', 'string')
        elem.text = str(value)


def json_to_xml(json_data, root_name='root'):
    """
    Convert JSON data to XML ElementTree.
    
    Args:
        json_data: The JSON data (dict or list)
        root_name: Name for the root XML element
    
    Returns:
        xml.etree.ElementTree.ElementTree: The XML tree
    """
    # Create root element
    root = ET.Element(sanitize_tag_name(root_name))
    
    if isinstance(json_data, dict):
        # If root is a dictionary, add its contents to root
        for key, value in json_data.items():
            convert_value_to_xml(root, key, value)
    elif isinstance(json_data, list):
        # If root is a list, create items
        root.set('type', 'array')
        root.set('length', str(len(json_data)))
        for i, item in enumerate(json_data):
            convert_value_to_xml(root, f'item_{i}', item)
    else:
        # If root is a simple value
        root.text = str(json_data)
        root.set('type', type(json_data).__name__)
    
    return ET.ElementTree(root)


def prettify_xml(elem):
    """
    Return a pretty-printed XML string for the Element.
    """
    rough_string = ET.tostring(elem, 'unicode')
    reparsed = minidom.parseString(rough_string)
    return reparsed.toprettyxml(indent="  ")


def convert_file(input_file, output_file=None, root_name='root'):
    """
    Convert a JSON file to XML file.
    
    Args:
        input_file: Path to input JSON file
        output_file: Path to output XML file (optional)
        root_name: Name for the root XML element
    """
    try:
        # Read JSON file
        with open(input_file, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        # Convert to XML
        xml_tree = json_to_xml(json_data, root_name)
        
        # Generate output filename if not provided
        if output_file is None:
            input_path = Path(input_file)
            output_file = input_path.with_suffix('.xml')
        
        # Write XML file with pretty formatting
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(prettify_xml(xml_tree.getroot()))
        
        print(f"Successfully converted '{input_file}' to '{output_file}'")
        return True
        
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
        return False
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in '{input_file}': {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False


def main():
    """Main function to handle command line arguments and execute conversion."""
    parser = argparse.ArgumentParser(
        description='Convert JSON file to XML format',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python json_to_xml_converter.py q4_3.json
  python json_to_xml_converter.py q4_3.json -o devices.xml
  python json_to_xml_converter.py q4_3.json -r devices
        """
    )
    
    parser.add_argument('input_file', help='Input JSON file path')
    parser.add_argument('-o', '--output', help='Output XML file path (default: input_file.xml)')
    parser.add_argument('-r', '--root', default='root', help='Root element name (default: root)')
    
    args = parser.parse_args()
    
    # Convert the file
    success = convert_file(args.input_file, args.output, args.root)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
