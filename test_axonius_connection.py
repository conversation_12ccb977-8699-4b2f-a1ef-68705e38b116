#!/usr/bin/env python3
"""
Quick Axonius Connection Test

This script provides a simple way to test your Axonius connection
using your existing configuration.
"""

import json
import sys
import argparse
from pathlib import Path
from axonius_api_client import AxoniusClient, AxoniusConfig, load_config_from_env, load_config_from_file


def test_with_config_file(config_file):
    """Test connection using configuration file."""
    print(f"Testing connection with config file: {config_file}")
    
    try:
        config = load_config_from_file(config_file)
        print(f"✓ Configuration loaded")
        print(f"  URL: {config.base_url}")
        print(f"  SSL Verification: {config.verify_ssl}")
        
    except Exception as e:
        print(f"✗ Failed to load configuration: {e}")
        return False
    
    return test_connection(config)


def test_with_env_vars():
    """Test connection using environment variables."""
    print("Testing connection with environment variables")
    
    try:
        config = load_config_from_env()
        print(f"✓ Configuration loaded from environment")
        print(f"  URL: {config.base_url}")
        print(f"  SSL Verification: {config.verify_ssl}")
        
    except Exception as e:
        print(f"✗ Failed to load configuration from environment: {e}")
        return False
    
    return test_connection(config)


def test_connection(config):
    """Test the actual connection."""
    print("\nTesting API connection...")
    
    try:
        client = AxoniusClient(config)
        
        if client.test_connection():
            print("✓ Connection successful!")
            
            # Try to get device count
            try:
                count = client.get_devices_count()
                print(f"✓ Device count: {count}")
            except Exception as e:
                print(f"⚠ Could not get device count: {e}")
            
            return True
        else:
            print("✗ Connection failed!")
            return False
            
    except Exception as e:
        print(f"✗ Connection error: {e}")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Test Axonius API connection')
    parser.add_argument('--config', help='Configuration file path')
    parser.add_argument('--env', action='store_true', help='Use environment variables')
    
    args = parser.parse_args()
    
    print("Axonius Connection Test")
    print("=" * 30)
    
    success = False
    
    if args.config:
        success = test_with_config_file(args.config)
    elif args.env:
        success = test_with_env_vars()
    else:
        # Try to auto-detect configuration
        if Path('axonius_config.json').exists():
            print("Found axonius_config.json, using it...")
            success = test_with_config_file('axonius_config.json')
        else:
            print("No configuration file found, trying environment variables...")
            try:
                success = test_with_env_vars()
            except:
                print("No environment variables found either.")
                print("\nUsage:")
                print("  python test_axonius_connection.py --config axonius_config.json")
                print("  python test_axonius_connection.py --env")
                return 1
    
    if success:
        print("\n✓ All tests passed! Your Axonius connection is working.")
    else:
        print("\n✗ Connection test failed.")
        print("\nTroubleshooting steps:")
        print("1. Run the diagnostic tool:")
        print("   python diagnose_axonius_api.py --url YOUR_URL --api-key YOUR_KEY --api-secret YOUR_SECRET")
        print("2. Check your configuration file or environment variables")
        print("3. Verify your Axonius instance URL and credentials")
    
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
