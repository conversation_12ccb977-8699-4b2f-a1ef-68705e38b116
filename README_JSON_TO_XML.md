# JSON to XML Converter

This repository contains Python scripts to convert JSON files to XML format, specifically designed to handle the `q4_3.json` file structure.

## Files

- `json_to_xml_converter.py` - Main converter script with command-line interface
- `convert_q4_3.py` - Simple convenience script for converting q4_3.json
- `q4_3.json` - Input JSON file (device data)
- `q4_3.xml` - Output XML file (generated)

## Features

- Handles complex nested JSON structures
- Preserves data types with XML attributes
- Sanitizes field names to be valid XML element names
- Pretty-prints XML output for readability
- Command-line interface with options
- Handles arrays, objects, null values, and primitive types

## Usage

### Method 1: Using the main converter script

```bash
# Basic conversion
python3 json_to_xml_converter.py q4_3.json

# Specify output file and root element name
python3 json_to_xml_converter.py q4_3.json -o devices.xml -r devices

# Get help
python3 json_to_xml_converter.py --help
```

### Method 2: Using the convenience script

```bash
python3 convert_q4_3.py
```

## Command Line Options

- `input_file` - Path to the input JSON file (required)
- `-o, --output` - Output XML file path (optional, defaults to input_file.xml)
- `-r, --root` - Root element name (optional, defaults to 'root')

## XML Structure

The converter creates XML with the following features:

- **Type preservation**: Each element has a `type` attribute indicating the original JSON data type
- **Array handling**: Arrays include a `length` attribute and use `item_N` naming for elements
- **Null handling**: Null values are represented as empty elements with `type="null"`
- **Field name sanitization**: JSON field names with dots, dashes, or spaces are converted to valid XML element names

### Example

JSON:
```json
{
  "type": "devices",
  "entities": [
    {
      "adapter_list_length": 3,
      "adapters": ["infoblox_adapter", "mssql_adapter"],
      "internal_axon_id": "e9b048e0171c78f479db8463d63a9a4c"
    }
  ]
}
```

XML:
```xml
<devices>
  <type type="string">devices</type>
  <entities type="array" length="1">
    <item_0 type="object">
      <adapter_list_length type="integer">3</adapter_list_length>
      <adapters type="array" length="2">
        <item_0 type="string">infoblox_adapter</item_0>
        <item_1 type="string">mssql_adapter</item_1>
      </adapters>
      <internal_axon_id type="string">e9b048e0171c78f479db8463d63a9a4c</internal_axon_id>
    </item_0>
  </entities>
</devices>
```

## Requirements

- Python 3.6 or higher
- Standard library modules only (no external dependencies)

## Error Handling

The script handles common errors:
- File not found
- Invalid JSON format
- Permission errors
- Invalid characters in field names

## Notes

- The original `q4_3.json` file contains 96 device entities
- The generated XML file preserves all data and structure
- Field names with dots (e.g., `adapters_data.mssql_adapter.mssql__serialnumber`) are converted to underscores for valid XML
- Empty strings and null values are properly distinguished in the XML output
