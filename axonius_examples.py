#!/usr/bin/env python3
"""
Axonius API Examples

This script demonstrates various ways to use the Axonius API client
for different data retrieval scenarios.
"""

import json
import logging
from datetime import datetime
from axonius_api_client import AxoniusClient, AxoniusConfig, AxoniusAPIError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_basic_connection():
    """Example: Basic connection and testing."""
    print("\n=== Example: Basic Connection ===")
    
    # Create configuration (replace with your actual values)
    config = AxoniusConfig(
        base_url="https://demo01.axoniusgov.com",
        api_key="NUEIoZeHTquKE6GVUNSetFlp3NwZCCdPw3leZY5hXxE",
        api_secret="DxP19hxeZriGWR2rMkzgU3u0um_vQMJYQ724Q0EcW_o",
        verify_ssl=True
    )
    
    # Create client and test connection
    client = AxoniusClient(config)
    
    if client.test_connection():
        print("✓ Connection successful!")
    else:
        print("✗ Connection failed!")


def example_get_device_count():
    """Example: Get total device count."""
    print("\n=== Example: Get Device Count ===")
    
    config = AxoniusConfig(
        base_url="https://demo01.axoniusgov.com",
        api_key="NUEIoZeHTquKE6GVUNSetFlp3NwZCCdPw3leZY5hXxE",
        api_secret="DxP19hxeZriGWR2rMkzgU3u0um_vQMJYQ724Q0EcW_o",
        verify_ssl=True
    )
    
    client = AxoniusClient(config)
    
    try:
        # Get total count
        total_count = client.get_devices_count()
        print(f"Total devices: {total_count}")
        
        # Get count with query filter
        active_count = client.get_devices_count(query='adapter_asset_entities_info:"Active"')
        print(f"Active devices: {active_count}")
        
    except AxoniusAPIError as e:
        print(f"Error: {e}")


def example_paginated_retrieval():
    """Example: Retrieve devices with pagination."""
    print("\n=== Example: Paginated Retrieval ===")
    
    config = AxoniusConfig(
        base_url="https://demo01.axoniusgov.com",
        api_key="NUEIoZeHTquKE6GVUNSetFlp3NwZCCdPw3leZY5hXxE",
        api_secret="DxP19hxeZriGWR2rMkzgU3u0um_vQMJYQ724Q0EcW_o",
        verify_ssl=True
    )
    
    client = AxoniusClient(config)
    
    try:
        page_count = 0
        total_devices = 0
        
        # Iterate through pages
        for page_data in client.get_devices_paged(
            page_size=50,  # Small page size for demo
            max_pages=3    # Limit to first 3 pages
        ):
            page_count += 1
            entities = page_data.get('entities', [])
            total_devices += len(entities)
            
            print(f"Page {page_count}: {len(entities)} devices")
            print(f"  Total pages: {page_data.get('total_pages', 'Unknown')}")
            print(f"  Current page: {page_data.get('current_page', 'Unknown')}")
            
            # Show first device from this page
            if entities:
                first_device = entities[0]
                device_id = first_device.get('internal_axon_id', 'Unknown')
                adapters = first_device.get('adapters', [])
                print(f"  First device ID: {device_id}")
                print(f"  Adapters: {adapters[:3]}...")  # Show first 3 adapters
        
        print(f"\nTotal devices processed: {total_devices}")
        
    except AxoniusAPIError as e:
        print(f"Error: {e}")


def example_filtered_retrieval():
    """Example: Retrieve devices with filters and specific fields."""
    print("\n=== Example: Filtered Retrieval ===")
    
    config = AxoniusConfig(
        base_url="https://demo01.axoniusgov.com",
        api_key="NUEIoZeHTquKE6GVUNSetFlp3NwZCCdPw3leZY5hXxE",
        api_secret="DxP19hxeZriGWR2rMkzgU3u0um_vQMJYQ724Q0EcW_o",
        verify_ssl=True
    )
    
    client = AxoniusClient(config)
    
    try:
        # Define specific fields to retrieve
        fields = [
            'internal_axon_id',
            'adapters',
            'adapter_list_length',
            'adapters_data.mssql_adapter.mssql__serialnumber'
        ]
        
        # Query for devices with MSSQL adapter
        query = 'adapters:"mssql_adapter"'
        
        devices = client.get_all_devices(
            query=query,
            fields=fields,
            page_size=100,
            max_items=10  # Limit to 10 devices for demo
        )
        
        print(f"Found {len(devices)} devices with MSSQL adapter")
        
        for i, device in enumerate(devices[:3], 1):  # Show first 3
            print(f"\nDevice {i}:")
            print(f"  ID: {device.get('internal_axon_id', 'Unknown')}")
            print(f"  Adapters: {device.get('adapters', [])}")
            print(f"  Adapter count: {device.get('adapter_list_length', 'Unknown')}")
            
            # Show MSSQL serial number if available
            serial = device.get('adapters_data.mssql_adapter.mssql__serialnumber')
            if serial:
                print(f"  MSSQL Serial: {serial}")
        
    except AxoniusAPIError as e:
        print(f"Error: {e}")


def example_save_to_file():
    """Example: Save devices to file."""
    print("\n=== Example: Save to File ===")
    
    config = AxoniusConfig(
        base_url="https://demo01.axoniusgov.com",
        api_key="NUEIoZeHTquKE6GVUNSetFlp3NwZCCdPw3leZY5hXxE",
        api_secret="DxP19hxeZriGWR2rMkzgU3u0um_vQMJYQ724Q0EcW_o",
        verify_ssl=True
    )
    
    client = AxoniusClient(config)
    
    try:
        # Save all devices to JSON file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"axonius_devices_example_{timestamp}.json"
        
        client.save_devices_to_file(
            filename=filename,
            page_size=100,
            format_type='json'
        )
        
        print(f"Devices saved to {filename}")
        
        # Also save as JSON Lines format
        jsonl_filename = f"axonius_devices_example_{timestamp}.jsonl"
        client.save_devices_to_file(
            filename=jsonl_filename,
            page_size=100,
            format_type='jsonl'
        )
        
        print(f"Devices also saved to {jsonl_filename} (JSON Lines format)")
        
    except AxoniusAPIError as e:
        print(f"Error: {e}")


def example_error_handling():
    """Example: Error handling and retry logic."""
    print("\n=== Example: Error Handling ===")
    
    # Configuration with invalid credentials to demonstrate error handling
    config = AxoniusConfig(
        base_url="https://demo01.axoniusgov.com",
        api_key="NUEIoZeHTquKE6GVUNSetFlp3NwZCCdPw3leZY5hXxE",
        api_secret="DxP19hxeZriGWR2rMkzgU3u0um_vQMJYQ724Q0EcW_o",
        max_retries=2,
        retry_delay=1
    )
    
    client = AxoniusClient(config)
    
    try:
        # This should fail and demonstrate error handling
        client.test_connection()
        
    except AxoniusAPIError as e:
        print(f"Expected error caught: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")


def example_advanced_pagination():
    """Example: Advanced pagination with custom processing."""
    print("\n=== Example: Advanced Pagination ===")
    
    config = AxoniusConfig(
        base_url="https://demo01.axoniusgov.com",
        api_key="NUEIoZeHTquKE6GVUNSetFlp3NwZCCdPw3leZY5hXxE",
        api_secret="DxP19hxeZriGWR2rMkzgU3u0um_vQMJYQ724Q0EcW_o"
    )
    
    client = AxoniusClient(config)
    
    try:
        # Process devices in batches with custom logic
        adapter_stats = {}
        device_count = 0
        
        for page_data in client.get_devices_paged(
            page_size=100,
            fields=['adapters', 'internal_axon_id'],
            max_pages=5  # Limit for demo
        ):
            entities = page_data.get('entities', [])
            
            for device in entities:
                device_count += 1
                adapters = device.get('adapters', [])
                
                # Count adapter usage
                for adapter in adapters:
                    adapter_stats[adapter] = adapter_stats.get(adapter, 0) + 1
        
        print(f"Processed {device_count} devices")
        print("\nAdapter usage statistics:")
        for adapter, count in sorted(adapter_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"  {adapter}: {count} devices")
        
    except AxoniusAPIError as e:
        print(f"Error: {e}")


def main():
    """Run all examples."""
    print("Axonius API Client Examples")
    print("=" * 40)
    print("Note: Replace configuration values with your actual Axonius instance details")
    
    # Run examples (comment out the ones you don't want to run)
    example_basic_connection()
    example_get_device_count()
    example_paginated_retrieval()
    example_filtered_retrieval()
    example_save_to_file()
    example_error_handling()
    example_advanced_pagination()
    
    print("\n" + "=" * 40)
    print("Examples completed!")


if __name__ == '__main__':
    main()
