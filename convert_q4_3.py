#!/usr/bin/env python3
"""
Simple script to convert q4_3.json to XML format.
This is a convenience script that uses the json_to_xml_converter module.
"""

import json_to_xml_converter

def main():
    """Convert q4_3.json to q4_3.xml with appropriate settings."""
    
    input_file = "q4_3.json"
    output_file = "q4_3.xml"
    root_name = "devices"
    
    print(f"Converting {input_file} to {output_file}...")
    
    success = json_to_xml_converter.convert_file(
        input_file=input_file,
        output_file=output_file,
        root_name=root_name
    )
    
    if success:
        print("Conversion completed successfully!")
        print(f"Output file: {output_file}")
    else:
        print("Conversion failed!")

if __name__ == '__main__':
    main()
