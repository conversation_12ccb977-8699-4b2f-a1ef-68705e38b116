#!/usr/bin/env python3
"""
Explore Axonius API Structure

This script explores the actual API responses to understand
the correct structure and parameters.
"""

import requests
import json
import sys
from axonius_api_client import load_config_from_file
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def explore_api():
    """Explore the API structure."""
    
    # Load configuration
    config = load_config_from_file('axonius_config.json')
    
    # Set up session
    session = requests.Session()
    session.verify = config.verify_ssl
    session.headers.update({
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'api-key': config.api_key,
        'api-secret': config.api_secret
    })
    
    print("Exploring Axonius API Structure")
    print("=" * 40)
    
    # Test the working endpoint
    print("\n1. Testing /api/devices/count")
    print("-" * 30)
    
    try:
        response = session.get(f"{config.base_url}/api/devices/count")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Parsed JSON: {json.dumps(data, indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Try different approaches to get devices
    print("\n2. Testing different device endpoints")
    print("-" * 40)
    
    endpoints_to_try = [
        ('/api/devices', 'GET', {}),
        ('/api/devices', 'POST', {'page': 1, 'page_size': 5}),
        ('/api/devices', 'POST', {'saved_query': 'all', 'page': 1}),
        ('/api/devices', 'POST', {'query': '', 'page': 1, 'page_size': 5}),
        ('/api/devices', 'POST', {}),
        ('/api/devices/search', 'POST', {'page': 1, 'page_size': 5}),
        ('/api/devices/query', 'POST', {'page': 1, 'page_size': 5}),
    ]
    
    for endpoint, method, payload in endpoints_to_try:
        print(f"\nTrying: {method} {endpoint}")
        if payload:
            print(f"Payload: {json.dumps(payload)}")
        
        try:
            if method == 'GET':
                response = session.get(f"{config.base_url}{endpoint}")
            else:
                response = session.post(f"{config.base_url}{endpoint}", json=payload)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"Success! Response keys: {list(data.keys())}")
                    
                    # Show structure
                    if 'entities' in data:
                        entities = data['entities']
                        print(f"Found {len(entities)} entities")
                        if entities:
                            print(f"First entity keys: {list(entities[0].keys())[:10]}")
                    
                    # Save successful response for analysis
                    with open(f'api_response_{method.lower()}_{endpoint.replace("/", "_")}.json', 'w') as f:
                        json.dump(data, f, indent=2)
                    print(f"Response saved to file")
                    
                except json.JSONDecodeError:
                    print(f"Response not JSON: {response.text[:200]}")
            else:
                print(f"Error response: {response.text[:200]}")
                
        except Exception as e:
            print(f"Request failed: {e}")
    
    # Try to understand the query structure from your original file
    print("\n3. Analyzing original q4_3.json structure")
    print("-" * 45)
    
    try:
        with open('q4_3.json', 'r') as f:
            original_data = json.load(f)
        
        print(f"Original file structure:")
        print(f"  Type: {original_data.get('type')}")
        print(f"  Entities count: {len(original_data.get('entities', []))}")
        print(f"  Saved query: {original_data.get('saved_query')}")
        print(f"  Page: {original_data.get('page')}")
        
        # Try to use the same structure
        if original_data.get('saved_query') and original_data.get('page'):
            print(f"\nTrying with original structure...")
            payload = {
                'saved_query': original_data.get('saved_query'),
                'page': 1,  # Start with page 1
                'page_size': 5
            }
            
            response = session.post(f"{config.base_url}/api/devices", json=payload)
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"Success with original structure! Keys: {list(data.keys())}")
            else:
                print(f"Failed: {response.text[:200]}")
                
    except Exception as e:
        print(f"Error analyzing original file: {e}")


if __name__ == '__main__':
    explore_api()
